import { Suspense, lazy, forwardRef } from "react";
import { motion } from "framer-motion";
import {
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { useDroppable } from "@dnd-kit/core";
import { Todo } from "../types/todo";
import clsx from "clsx";

// Lazy load TodoItem
const TodoItem = lazy(() => import("./TodoItem"));

// Drop zone component for visual drop indicators
const DropZone = ({ dropId }: { dropId: string }) => {
  const { setNodeRef, isOver } = useDroppable({
    id: dropId,
  });

  return (
    <div
      ref={setNodeRef}
      className={clsx(
        "transition-all duration-200",
        isOver ? "h-2 my-2" : "h-0 my-0"
      )}
    >
      {isOver && (
        <div className="relative h-2">
          <div className="absolute inset-0 bg-purple-500 rounded-full shadow-lg animate-pulse" />
          <div className="absolute -left-2 -right-2 h-2 bg-purple-400 rounded-full opacity-50" />
        </div>
      )}
    </div>
  );
};

interface TodoListItemsProps {
  filteredTodos: Todo[];
  onToggle: (id: string) => Promise<void>;
  onDelete: (id: string) => Promise<void>;
  onOpenEditDialog: (todo: Todo) => void;
}

const TodoListItems = forwardRef<HTMLDivElement, TodoListItemsProps>(
  ({ filteredTodos, onToggle, onDelete, onOpenEditDialog }, ref) => {
    if (filteredTodos.length === 0) {
      return (
        <motion.div
          ref={ref}
          className="text-center text-gray-500 dark:text-gray-400 py-8"
        >
          No todos yet. Add one above!
        </motion.div>
      );
    }

    return (
      <motion.div ref={ref} layout>
        <SortableContext
          items={filteredTodos.map((t) => t.id)}
          strategy={verticalListSortingStrategy}
        >
          <div className="space-y-2">
            <Suspense fallback={<div>Loading todos...</div>}>
              {filteredTodos.map((todo, index) => {
                // Create drop zone IDs for before and after each item
                const beforeDropId = `drop-before-${todo.id}`;
                const afterDropId = `drop-after-${todo.id}`;

                return (
                  <div key={todo.id}>
                    {/* Drop indicator before first item */}
                    {index === 0 && <DropZone dropId={beforeDropId} />}

                    {/* The todo item */}
                    <TodoItem
                      todo={todo}
                      onToggle={onToggle}
                      onDelete={onDelete}
                      onOpenEditDialog={onOpenEditDialog}
                    />

                    {/* Drop indicator after each item */}
                    <DropZone dropId={afterDropId} />
                  </div>
                );
              })}
            </Suspense>
          </div>
        </SortableContext>
      </motion.div>
    );
  }
);

// Define displayName for better debugging
TodoListItems.displayName = "TodoListItems";

export default TodoListItems;